import { cors } from 'hono/cors'
import createApp from '@/lib/create-app'
import { apiLogger } from '@/lib/logger'
import { getAccessToken } from './redis'
import { sendBatchMessages, sendMessage, topics } from './services/kafka'
import { kafkaScheduler } from './services/kafka-scheduler'
import { checkAndExecuteScheduledTasks } from './services/scheduler'
import { execSubscribe, handleSubscribe } from './subscribe'
import { TaskType } from './types/task-message'
import 'dotenv/config'

const app = createApp()

app.use('/posts/*', cors())

// 根路径处理
app.get('/', (c) => {
  return c.json({
    name: 'NQHY Backend API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
  })
})

app.post('/subscribeMessage', handleSubscribe)
app.post('/subscribeMessageSingle', execSubscribe)
app.post('/check', checkAndExecuteScheduledTasks)

app.get('/douyin/token/:appId', async (c) => {
  const appId = c.req.param('appId')
  apiLogger.info('获取抖音access token请求', { appId })

  try {
    const accessToken = await getAccessToken(appId)

    if (accessToken) {
      apiLogger.info('成功获取access token', { appId })
      return c.json({
        success: true,
        access_token: accessToken,
        appId,
      })
    }
    else {
      apiLogger.warn('未找到access token', { appId })
      return c.json({
        success: false,
        message: `未找到appId ${appId} 的access token`,
      }, 404)
    }
  }
  catch (error) {
    apiLogger.error('获取access token失败', { appId, error: error.message })
    return c.json({
      success: false,
      error: error.message,
    }, 500)
  }
})

// Kafka消息发送API
app.post('/kafka/send', async (c) => {
  try {
    const body = await c.req.json()
    const { topic, message, key, partition } = body

    if (!topic || !message) {
      return c.json({
        success: false,
        error: 'topic和message参数是必需的',
      }, 400)
    }

    if (!Object.values(topics).includes(topic)) {
      return c.json({
        success: false,
        error: `无效的topic: ${topic}`,
        availableTopics: Object.values(topics),
      }, 400)
    }

    apiLogger.info('发送Kafka消息', { topic, key })

    await sendMessage(topic, message, key, partition)

    return c.json({
      success: true,
      message: '消息发送成功',
      topic,
      key,
    })
  }
  catch (error) {
    apiLogger.error('发送Kafka消息失败', { error: error.message })
    return c.json({
      success: false,
      error: error.message,
    }, 500)
  }
})

// Kafka批量消息发送API
app.post('/kafka/send-batch', async (c) => {
  try {
    const body = await c.req.json()
    const { topic, messages } = body

    if (!topic || !Array.isArray(messages) || messages.length === 0) {
      return c.json({
        success: false,
        error: 'topic和messages参数是必需的，messages必须是非空数组',
      }, 400)
    }

    if (!Object.values(topics).includes(topic)) {
      return c.json({
        success: false,
        error: `无效的topic: ${topic}`,
        availableTopics: Object.values(topics),
      }, 400)
    }

    apiLogger.info('批量发送Kafka消息', { topic, messageCount: messages.length })

    await sendBatchMessages(topic, messages)

    return c.json({
      success: true,
      message: '批量消息发送成功',
      topic,
      messageCount: messages.length,
    })
  }
  catch (error) {
    apiLogger.error('批量发送Kafka消息失败', { error: error.message })
    return c.json({
      success: false,
      error: error.message,
    }, 500)
  }
})

// 获取可用的Kafka主题
app.get('/kafka/topics', async (c) => {
  return c.json({
    success: true,
    topics: Object.entries(topics).map(([key, value]) => ({
      name: key,
      topic: value,
    })),
  })
})

// 手动触发定时任务
app.post('/scheduler/trigger', async (c) => {
  try {
    const body = await c.req.json()
    const { taskType, parameters } = body

    if (!taskType) {
      return c.json({
        success: false,
        error: 'taskType参数是必需的',
      }, 400)
    }

    // 检查调度器模式
    const schedulerMode = process.env.SCHEDULER_MODE || 'kafka'

    if (schedulerMode === 'kafka') {
      const taskId = await kafkaScheduler.triggerTask(taskType, parameters || {})

      apiLogger.info('手动触发Kafka任务', { taskType, taskId })

      return c.json({
        success: true,
        message: '任务已发送到Kafka队列',
        taskId,
        taskType,
        mode: 'kafka',
      })
    }
    else if (schedulerMode === 'direct') {
      // 使用传统调度器
      const { triggerMessageTask } = await import('./services/scheduler')
      await triggerMessageTask(taskType, parameters)

      apiLogger.info('手动触发传统任务', { taskType })

      return c.json({
        success: true,
        message: '任务已直接执行',
        taskType,
        mode: 'direct',
      })
    }
    else if (schedulerMode === 'disabled') {
      return c.json({
        success: false,
        error: '定时任务调度器已禁用',
        mode: 'disabled',
      }, 400)
    }
    else {
      return c.json({
        success: false,
        error: `未知的调度器模式: ${schedulerMode}`,
        availableModes: ['kafka', 'direct', 'disabled'],
      }, 400)
    }
  }
  catch (error) {
    apiLogger.error('手动触发任务失败', { error: error.message })
    return c.json({
      success: false,
      error: error.message,
    }, 500)
  }
})

// 获取调度器配置
app.get('/scheduler/config', async (c) => {
  try {
    const schedulerMode = process.env.SCHEDULER_MODE || 'kafka'

    switch (schedulerMode) {
      case 'kafka': {
        const configs = kafkaScheduler.getScheduleConfigs()
        return c.json({
          success: true,
          mode: 'kafka',
          configs,
          availableTaskTypes: Object.values(TaskType),
        })
      }

      case 'direct':
        return c.json({
          success: true,
          mode: 'direct',
          message: '使用传统调度器模式',
        })

      case 'disabled':
        return c.json({
          success: true,
          mode: 'disabled',
          message: '定时任务调度器已禁用',
        })

      default:
        return c.json({
          success: false,
          error: `未知的调度器模式: ${schedulerMode}`,
          availableModes: ['kafka', 'direct', 'disabled'],
        }, 400)
    }
  }
  catch (error) {
    apiLogger.error('获取调度器配置失败', { error: error.message })
    return c.json({
      success: false,
      error: error.message,
    }, 500)
  }
})

// 404处理
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: `路径 ${c.req.path} 不存在`,
    timestamp: new Date().toISOString(),
  }, 404)
})

export default app
