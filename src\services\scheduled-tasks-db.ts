import type { ScheduledMessageTask } from '@/config'

import { eq } from 'drizzle-orm'
import { schedulerLogger } from '@/lib/logger'
import { scheduledTasks } from '@/schema'
import { getDb } from '../db'

/**
 * 从数据库加载定时任务配置
 */
export async function loadScheduledTasksFromDB(): Promise<ScheduledMessageTask[]> {
  try {
    const db = await getDb()
    schedulerLogger.info('开始从数据库加载定时任务配置')

    // 查询启用的任务
    const tasks = await db
      .select()
      .from(scheduledTasks)
      .where(eq(scheduledTasks.enabled, true))
      .orderBy(scheduledTasks.id)

    // 转换为ScheduledMessageTask格式
    const result: ScheduledMessageTask[] = tasks.map(task => ({
      tplId: task.tplId,
      messageData: task.messageData as Record<string, string>[], // JSON数组直接转换
      platform: task.platform,
      appId: task.appId,
      max: task.max,
      cronExpression: task.cronExpression,
      intervalHours: task.intervalHours || 24, // 默认24小时
      enabled: task.enabled,
      type: task.type || 'recurring', // 默认为循环任务
    }))

    schedulerLogger.info('从数据库加载定时任务配置完成', {
      totalTasks: result.length,
      enabledTasks: result.filter(t => t.enabled).length,
    })

    return result
  }
  catch (error) {
    // 构建详细的错误信息
    const errorInfo: any = {
      message: error.message || 'Unknown error',
      name: error.name || 'Error',
    }

    // 安全地添加可能存在的错误属性
    if (error.code)
      errorInfo.code = error.code
    if (error.errno)
      errorInfo.errno = error.errno
    if (error.sqlState)
      errorInfo.sqlState = error.sqlState
    if (error.sqlMessage)
      errorInfo.sqlMessage = error.sqlMessage
    if (error.stack)
      errorInfo.stack = error.stack

    schedulerLogger.error('从数据库加载定时任务配置失败', errorInfo)

    // 也输出到控制台以便调试
    console.error('数据库加载失败详细信息:', error)

    // 返回空数组，避免应用崩溃
    return []
  }
}

/**
 * 创建新的定时任务
 */
export async function createScheduledTask(
  taskData: Omit<typeof scheduledTasks.$inferInsert, 'id' | 'createdAt' | 'updatedAt'>,
): Promise<number> {
  try {
    const db = await getDb()

    // 插入任务（messageData已包含在taskData中）
    const [task] = await db.insert(scheduledTasks).values(taskData)
    const taskId = Number(task.insertId)

    schedulerLogger.info('创建定时任务成功', {
      taskId,
      tplId: taskData.tplId,
      messageCount: Array.isArray(taskData.messageData) ? taskData.messageData.length : 0,
    })

    return taskId
  }
  catch (error) {
    schedulerLogger.error('创建定时任务失败', {
      error: error.message,
      tplId: taskData.tplId,
    })
    throw error
  }
}

/**
 * 更新任务启用状态
 */
export async function updateTaskEnabled(taskId: number, enabled: boolean): Promise<void> {
  try {
    const db = await getDb()
    await db
      .update(scheduledTasks)
      .set({ enabled, updatedAt: new Date() })
      .where(eq(scheduledTasks.id, taskId))

    schedulerLogger.info('更新任务状态成功', { taskId, enabled })
  }
  catch (error) {
    schedulerLogger.error('更新任务状态失败', {
      error: error.message,
      taskId,
      enabled,
    })
    throw error
  }
}

/**
 * 删除定时任务
 */
export async function deleteScheduledTask(taskId: number): Promise<void> {
  try {
    const db = await getDb()

    // 直接删除任务（单表设计）
    await db.delete(scheduledTasks).where(eq(scheduledTasks.id, taskId))

    schedulerLogger.info('删除定时任务成功', { taskId })
  }
  catch (error) {
    schedulerLogger.error('删除定时任务失败', {
      error: error.message,
      taskId,
    })
    throw error
  }
}

/**
 * 获取所有任务列表（包括禁用的）
 */
export async function getAllScheduledTasks() {
  try {
    const db = await getDb()
    const tasks = await db
      .select()
      .from(scheduledTasks)
      .orderBy(scheduledTasks.id)

    return tasks
  }
  catch (error) {
    schedulerLogger.error('获取任务列表失败', { error: error.message })
    throw error
  }
}
