import { getDb } from '../db'
import { userSubscribe, scheduledTasks } from '../schema'
import { eq, and, lt } from 'drizzle-orm'
import { getAccessToken } from '@/redis'
import { formatChinaTime } from '@/utils/time'
import { logger } from '@/lib/logger'

interface DouyinMessageData {
  access_token: string
  tpl_id: string
  app_id: string
  open_id: string
  data?: Record<string,string>
  page?:string
}

interface DouyinMessageResponse {
  err_no: number
  err_tips: string
}

/**
 * 发送抖音订阅消息
 * @param accessToken 小程序access_token
 * @param messageData 消息数据
 */
export async function sendDouyinSubscribeMessage(  messageData: DouyinMessageData
): Promise<DouyinMessageResponse> {
  const url = `https://developer.toutiao.com/api/apps/subscribe_notification/developer/v1/notify`
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(messageData)
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json() as DouyinMessageResponse
    return result
  } catch (error) {
    console.error('发送抖音订阅消息失败:', error)
    throw error
  }
}
export async function sendBatchSubscribeMessagesSingle({appId,page,messageData,tplId,platform},{openId,id,count}: {
  id: string;
  openId: string;
  count: number;
}){
  try {
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
    const accessToken = await getAccessToken(appId) 
    if (!accessToken) {
      throw new Error(`未找到平台 ${platform} 应用 ${appId} 的 access_token`)
    }
    
    const result = await sendDouyinSubscribeMessage({
      access_token: accessToken,
      app_id: appId,
      open_id: openId,
      page: page,
      data: messageData,
      tpl_id: tplId
    })
    
    // 成功发送后更新计数和领取时间
    if (result.err_no === 0 && count !== null) {
      const updateTime = new Date()
      await db
        .update(userSubscribe)
        .set({
          count: (count || 0) + 1,
          lastReceivedAt: updateTime,
          updatedAt: updateTime
        })
        .where(eq(userSubscribe.id, id))
        .execute()
    }
    console.log(`发送给用户 ${openId}: ${result.err_no === 0 ? '成功' : result.err_tips}`)

    return {
      openId: openId,
      success: result.err_no,
      error:  result.err_tips 
    }
  } catch (error) {
    const accessToken = "1e8da803e7f25ceecee6e96e6b9f3b9309ea53ae"
    console.error(`发送给用户 ${openId} 失败:`, error)
    return {
      appId,
      tplId,
      platform,
      id,
      count,
      messageData,
      page,
      accessToken,
      openId: openId,
      success: false,
      error: error instanceof Error ? error : '未知错误'
    }
  }
}
/**
 * 批量发送订阅消息给符合条件的用户
 * @param tplId 模板ID
 * @param messageData 消息内容数据
 * @param accessToken 访问令牌
 */
export async function sendBatchSubscribeMessages(
task
) {
  const {
    tplId,
    messageData,
    page,
    max
  } = task
  const db = await getDb()
  if (!db) {
    throw new Error('数据库未初始化')
  }

  // 首先检查任务是否今天已经执行过
  const taskInfo = await db
    .select()
    .from(scheduledTasks)
    .where(eq(scheduledTasks.tplId, tplId))
    .limit(1)
    .execute()

  if (taskInfo.length === 0) {
    throw new Error(`未找到模板ID为 ${tplId} 的任务配置`)
  }

  const taskRecord = taskInfo[0]
  const now = new Date()

  // 检查任务是否今天已经执行过
  if (taskRecord.lastExecutedAt) {
    const lastExecuteDate = new Date(taskRecord.lastExecutedAt)
    const currentDate = new Date(now)

    // 比较日期部分，如果是同一天则跳过
    const lastDateString = lastExecuteDate.toDateString()
    const currentDateString = currentDate.toDateString()

    if (lastDateString === currentDateString) {
      logger.info(`任务 ${tplId} 今天已经执行过，跳过推送`)
      logger.info(`上次执行时间: ${formatChinaTime(taskRecord.lastExecutedAt)} (CST)`)
      logger.info(`上次执行日期: ${lastDateString}`)
      logger.info(`当前日期: ${currentDateString}`)
      return {
        totalUsers: 0,
        results: [],
        message: `任务今天已经执行过`
      }
    }
  }
  // 推送完成后，更新任务的 lastExecutedAt 时间，标记已执行
  const updateTime = new Date()

  await db
    .update(scheduledTasks)
    .set({
      lastExecutedAt: updateTime,
      updatedAt: updateTime // 同时更新 updatedAt 以保持记录的一致性
    })
    .where(eq(scheduledTasks.tplId, tplId))
    .execute()
  // 查询已接受订阅的用户，同时考虑用户的上次领取时间
  const allSubscribers = await db
    .select()
    .from(userSubscribe)
    .where(and(
      eq(userSubscribe.tplId, tplId),
      eq(userSubscribe.status, 'accept'),
      lt(userSubscribe.count, max)
    ))
    .execute()

  // 过滤出可以接收消息的用户（考虑个人领取间隔）
  const subscribers = allSubscribers.filter(subscriber => {
    // 如果用户从未领取过，可以发送
    if (!subscriber.lastReceivedAt) {
      return true
    }

    // 检查是否已经过了一天（跨日期）
    const lastReceiveDate = new Date(subscriber.lastReceivedAt)
    const currentDate = new Date(now)

    // 比较日期部分，只要不是同一天就可以发送
    const lastDateString = lastReceiveDate.toDateString()
    const currentDateString = currentDate.toDateString()

    return lastDateString !== currentDateString
  })

  logger.info(`找到 ${allSubscribers.length} 个用户接受了模板 ${tplId} 的订阅`)
  logger.info(`其中 ${subscribers.length} 个用户满足领取间隔条件（跨日期）`)

  if (subscribers.length === 0) {
    return {
      totalUsers: 0,
      results: [],
      message: '没有符合条件的用户'
    }
  }

  const results = []
  const accessToken = await getAccessToken(task.appId) 
  if (!accessToken) {
    throw new Error(`未找到平台 ${task.platform} 应用 ${task.appId} 的 access_token`)
  }
  for (const subscriber of subscribers) {
    const result = await sendDouyinSubscribeMessage({
      access_token: accessToken,
      app_id: task.appId,
      open_id:subscriber.openId,
      page: task.page,
      data: messageData.at(subscriber.count || 0),
      tpl_id:task.tplId
    })
    
    // 成功发送后根据任务类型处理
    if (result.err_no === 0) {
      const newCount = (subscriber.count || 0) + 1
      const updateTime = new Date()

      // 检查任务类型
      if (taskRecord.type === 'single') {
        // 单次任务：删除订阅记录
        await db
          .delete(userSubscribe)
          .where(eq(userSubscribe.id, subscriber.id))
          .execute()

        logger.info(`发送给用户 ${subscriber.openId}: 成功 (单次任务，已删除订阅记录)`)
      } else {
        // 循环任务：更新计数和时间戳
        await db
          .update(userSubscribe)
          .set({
            count: newCount,
            updatedAt: updateTime,
            lastReceivedAt: updateTime // 记录用户上次领取时间
          })
          .where(eq(userSubscribe.id, subscriber.id))
          .execute()

        logger.info(`发送给用户 ${subscriber.openId}: 成功 (第${newCount}次推送，领取时间: ${formatChinaTime(updateTime)} (CST))`)
      }
    } else {
      logger.error(`发送给用户 ${subscriber.openId}: 失败 - ${result.err_tips}`)
    }

    results.push(result)
  }

  logger.info(`任务 ${tplId} 推送完成，更新执行时间: ${formatChinaTime(updateTime)} (CST)`)

  return {
    totalUsers: subscribers.length,
    results,
    executedAt: updateTime
  }
}