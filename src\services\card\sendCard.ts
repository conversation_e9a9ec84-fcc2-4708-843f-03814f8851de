import { ScheduledMessageTask } from "@/config"
import { getDb } from "@/db"
import logger from "@/lib/logger"
import { scheduledTasks, xthyUserData } from "@/schema"
import { BaseTaskResult } from "@/types/task-message"
import { formatChinaTime, getChinaTime } from "@/utils/time"
import { and, eq, gte, lte } from "drizzle-orm"
import { batchPushFeedCard, createCardData } from "../card-push"

/**
 * 批量发送卡片给符合条件的用户
 * @param task 任务配置
 */
export async function sendBatchCards(task:ScheduledMessageTask): Promise<BaseTaskResult> {
    const {
      tplId,
      messageData,
      page,
      max
    } = task
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
  
    // 查询昨天到5天前注册的用户（签到用户）
    const now = getChinaTime()

    // 计算昨天和5天前的日期
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    yesterday.setHours(0, 0, 0, 0)

    const sixDaysAgo = new Date(now)
    sixDaysAgo.setDate(sixDaysAgo.getDate() - 5)
    sixDaysAgo.setHours(0, 0, 0, 0)

    const endOfYesterday = new Date(yesterday)
    endOfYesterday.setHours(23, 59, 59, 999)

    logger.info('查询签到用户', {
      sixDaysAgo: formatChinaTime(sixDaysAgo),
      endOfYesterday: formatChinaTime(endOfYesterday)
    })

    // 查询昨天到5天前注册的用户
    const allSubscribers = await db
      .select({
        openId: xthyUserData.openId,
        createTime: xthyUserData.createTime,
        nonId: xthyUserData.nonId
      })
      .from(xthyUserData)
      .where(and(
        gte(xthyUserData.createTime, sixDaysAgo),
        lte(xthyUserData.createTime, endOfYesterday)
      ))
      .execute()
  
    // 获取任务信息（用于判断任务类型）
    const taskInfo = await db
      .select()
      .from(scheduledTasks)
      .where(eq(scheduledTasks.tplId, tplId))
      .limit(1)
      .execute()
  
    if (taskInfo.length === 0) {
      throw new Error(`未找到模板ID为 ${tplId} 的任务配置`)
    }
  
    const taskRecord = taskInfo[0]

    // 为每个用户计算签到天数索引和奖励数据
    const subscribers = allSubscribers.map(user => {
      const createDate = new Date(user.createTime)
      const currentDate = new Date(now)

      // 计算天数差值
      const timeDiff = currentDate.getTime() - createDate.getTime()
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

      // 签到天数索引（1-6天）
      const dayIndex = Math.min(Math.max(daysDiff, 1), 7)

      return {
        openId: user.openId,
        nonId: user.nonId,
        createTime: user.createTime,
        dayIndex,
        // 根据dayIndex从messageData中获取对应的奖励数据
        rewardData: messageData[dayIndex - 1] || messageData[0] || {}
      }
    })
  
    logger.info(`找到 ${allSubscribers.length} 个用户接受了模板 ${tplId} 的卡片订阅`)
    logger.info(`其中 ${subscribers.length} 个用户满足领取间隔条件（跨日期）`)
  
    if (subscribers.length === 0) {
      return {
        totalUsers: 0,
        results: [],
        message: '没有符合条件的用户'
      }
    }
  
    const results = []

    // 发送卡片给签到用户
    for (const subscriber of subscribers) {
      try {
        // 使用用户的奖励数据创建卡片
        const cardData = createCardData(subscriber.rewardData)

        // 调用单用户卡片推送API
        const cardResult = await batchPushFeedCard(task.appId, {
          aid: 1, // 抖音
          mp_id: task.appId,
          card_id: parseInt(task.tplId), // 假设tplId是卡片ID
          data: cardData,
          open_id: [subscriber.openId]
        })

        // 判断发送结果
        const isSuccess = cardResult.successCount > 0
        const errorMsg = cardResult.successCount === 0 ? '发送失败' : null

        if (isSuccess) {
          logger.info(`发送签到卡片给用户 ${subscriber.openId}: 成功 (第${subscriber.dayIndex}天签到奖励)`)
        } else {
          logger.error(`发送签到卡片给用户 ${subscriber.openId}: 失败 - ${errorMsg}`)
        }

        results.push({
          openId: subscriber.openId,
          success: isSuccess,
          error: errorMsg,
          dayIndex: subscriber.dayIndex
        })
      } catch (error) {
        logger.error(`发送卡片给用户 ${subscriber.openId} 失败:`, error)
        results.push({
          openId: subscriber.openId,
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          dayIndex: subscriber.dayIndex
        })
      }
    }
  
    return {
      totalUsers: subscribers.length,
      results,
      message: `签到卡片发送完成，共处理 ${subscribers.length} 个用户`
    }
  }