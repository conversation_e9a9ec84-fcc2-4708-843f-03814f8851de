import { ScheduledMessageTask } from "@/config"
import { getDb } from "@/db"
import logger from "@/lib/logger"
import { scheduledTasks, userSubscribe } from "@/schema"
import { BaseTaskResult } from "@/types/task-message"
import { formatChinaTime, getChinaTime } from "@/utils/time"
import { and, eq, lt } from "drizzle-orm"
import { batchPushFeedCard, createCardData } from "../card-push"

/**
 * 批量发送卡片给符合条件的用户
 * @param task 任务配置
 */
export async function sendBatchCards(task:ScheduledMessageTask): Promise<BaseTaskResult> {
    const {
      tplId,
      messageData,
      page,
      max
    } = task
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
  
    // 查询已接受订阅的用户，同时考虑用户的上次领取时间
    const allSubscribers = await db
      .select()
      .from(userSubscribe)
      .where(and(
        eq(userSubscribe.tplId, tplId),
        eq(userSubscribe.status, 'accept'),
        lt(userSubscribe.count, max)
      ))
      .execute()
  
    // 获取任务信息（用于判断任务类型）
    const taskInfo = await db
      .select()
      .from(scheduledTasks)
      .where(eq(scheduledTasks.tplId, tplId))
      .limit(1)
      .execute()
  
    if (taskInfo.length === 0) {
      throw new Error(`未找到模板ID为 ${tplId} 的任务配置`)
    }
  
    const taskRecord = taskInfo[0]
    const now = getChinaTime()
  
    // 过滤出可以接收卡片的用户（考虑个人领取间隔）
    const subscribers = allSubscribers.filter(subscriber => {
      // 如果用户从未领取过，可以发送
      if (!subscriber.lastReceivedAt) {
        return true
      }
  
      // 检查是否已经过了一天（跨日期）
      const lastReceiveDate = new Date(subscriber.lastReceivedAt)
      const currentDate = now
  
      // 比较日期部分，只要不是同一天就可以发送
      const lastDateString = lastReceiveDate.toDateString()
      const currentDateString = currentDate.toDateString()
  
      return lastDateString !== currentDateString
    })
  
    logger.info(`找到 ${allSubscribers.length} 个用户接受了模板 ${tplId} 的卡片订阅`)
    logger.info(`其中 ${subscribers.length} 个用户满足领取间隔条件（跨日期）`)
  
    if (subscribers.length === 0) {
      return {
        totalUsers: 0,
        results: [],
        message: '没有符合条件的用户'
      }
    }
  
    const results = []
    // TODO: 这里需要实现具体的卡片发送API调用
    // 暂时模拟卡片发送成功
    for (const subscriber of subscribers) {
      try {
        // 模拟卡片发送API调用
        const cardData = createCardData(messageData.at(subscriber.count || 0))
        const cardResult = await batchPushFeedCard(task.appId,task.tplId,cardData,subscriber.openId)

        // 根据任务类型和发送结果处理
        if (taskRecord.type === 'single') {
          // 单次任务：无论成功失败都删除订阅记录
          await db
            .delete(userSubscribe)
            .where(eq(userSubscribe.id, subscriber.id))
            .execute()
  
          if (cardResult.err_no === 0) {
            logger.info(`发送卡片给用户 ${subscriber.openId}: 成功 (单次任务，已删除订阅记录)`)
          } else {
            logger.error(`发送卡片给用户 ${subscriber.openId}: 失败 - ${cardResult.err_tips} (单次任务，已删除订阅记录)`)
          }
        } else {
          // 循环任务：只有成功时才更新
          if (cardResult.err_no === 0) {
            const newCount = (subscriber.count || 0) + 1
            const updateTime = getChinaTime()
  
            await db
              .update(userSubscribe)
              .set({
                count: newCount,
                updatedAt: updateTime,
                lastReceivedAt: updateTime // 记录用户上次领取时间
              })
              .where(eq(userSubscribe.id, subscriber.id))
              .execute()
  
            logger.info(`发送卡片给用户 ${subscriber.openId}: 成功 (第${newCount}次推送，领取时间: ${formatChinaTime(updateTime)} (CST))`)
          } else {
            logger.error(`发送卡片给用户 ${subscriber.openId}: 失败 - ${cardResult.err_tips}`)
          }
        }
  
        results.push({
          openId: subscriber.openId,
          success: cardResult.err_no === 0,
          error: cardResult.err_no === 0 ? null : cardResult.err_tips
        })
      } catch (error) {
        logger.error(`发送卡片给用户 ${subscriber.openId} 失败:`, error)
        results.push({
          openId: subscriber.openId,
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }
  
    return {
      totalUsers: subscribers.length,
      results
    }
  }