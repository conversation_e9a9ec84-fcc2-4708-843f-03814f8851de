import { pinoLogger as logger } from 'hono-pino'
import pino from 'pino'
import pretty from 'pino-pretty'

// 根据环境变量确定日志级别
function getLogLevel() {
  return process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug')
}

// 根据环境确定是否使用pretty格式
function shouldUsePretty() {
  return process.env.NODE_ENV !== 'production'
}

// 创建Pino配置
function createPinoConfig() {
  const baseConfig = {
    level: getLogLevel(),
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label: string) => {
        return { level: label }
      },
    },
    serializers: {
      req: (req: any) => ({
        method: req.method,
        url: req.url,
        headers: req.headers,
        remoteAddress: req.socket?.remoteAddress,
        remotePort: req.socket?.remotePort,
      }),
      res: (res: any) => ({
        statusCode: res.statusCode,
        headers: res.getHeaders?.(),
      }),
      err: pino.stdSerializers.err,
    },
  }

  // 生产环境使用JSON格式，开发环境使用pretty格式
  if (shouldUsePretty()) {
    return pino(baseConfig, pretty({
      colorize: process.platform !== 'win32', // Windows下禁用颜色
      translateTime: 'yyyy-mm-dd HH:MM:ss',
      ignore: 'pid,hostname',
      messageFormat: '{req.method} {req.url} - {msg}',
    }))
  }
  else {
    return pino(baseConfig)
  }
}

export function pinoLogger() {
  return logger({
    pino: createPinoConfig(),
    http: {
      reqId: () => crypto.randomUUID(),
    },
  })
}
