/**
 * 时间工具函数
 */

/**
 * 格式化时间为中国时区字符串
 * @param date Date 对象
 * @returns 格式化的中国时区时间字符串
 */
export function formatChinaTime(date: Date): string {
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  })
}

/**
 * 格式化时间为中国时区 ISO 格式字符串
 * @param date Date 对象
 * @returns 格式化的中国时区 ISO 时间字符串
 */
export function formatChinaTimeISO(date: Date): string {
  const chinaTime = new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }))
  return chinaTime.toISOString().replace('Z', '+08:00')
}

/**
 * 获取当前中国时区时间
 * @returns 当前中国时区时间的 Date 对象
 */
export function getChinaTime(): Date {
  return new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }))
}
