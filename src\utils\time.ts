/**
 * 时间工具函数
 */
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// 启用插件
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * 格式化时间为中国时区字符串
 * @param date Date 对象
 * @returns 格式化的中国时区时间字符串
 */
export function formatChinaTime(date: Date): string {
  return dayjs(date).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化时间为中国时区 ISO 格式字符串
 * @param date Date 对象
 * @returns 格式化的中国时区 ISO 时间字符串
 */
export function formatChinaTimeISO(date: Date): string {
  return dayjs(date).tz('Asia/Shanghai').toISOString()
}

/**
 * 获取当前中国时区时间
 * @returns 当前中国时区时间的 Date 对象
 */
export function getChinaTime(): Date {
  return dayjs().tz('Asia/Shanghai').toDate()
}
