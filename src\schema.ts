import { mysqlTable, varchar, int, timestamp, bigint, index, boolean, text, json, datetime } from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { randomUUID } from 'crypto';

// 事件数据模型 (按日期分表)
export const eventData = mysqlTable('event_data', {
    id: bigint('id', { mode: 'number' }).primaryKey().autoincrement(),
    openId: varchar('open_id', { length: 255 }),
    eventCode: varchar('event_code', { length: 255 }),
    eventTime: timestamp('event_time'),
    uuid: varchar('uuid', { length: 255 }).unique(),
    appId: varchar('app_id', { length: 255 })
});
export const eventDataInsertSchema = createInsertSchema(eventData);
export const eventDataSelectSchema = createSelectSchema(eventData);

// 用户数据模型
export const userData = mysqlTable('xthy_user_data', {
    id: bigint('non_id', { mode: 'number' }).primaryKey().autoincrement(),
    appId: varchar('app_id', { length: 255 }),
    openId: varchar('open_id', { length: 255 }),
    unionId: varchar('union_id', { length: 255 }), 
    latestAdId: varchar('latest_ad_id', { length: 255 }),
    latestClickId: varchar('latest_click_id', { length: 255 }),
    clickIdCallbackFlag: varchar('click_id_callback_flag', { length: 1 }).default('N'),
    createTime: timestamp('create_time').defaultNow(),
    platform: varchar('platform', { length: 255 }),
    updateTime: timestamp('update_time').defaultNow(),
    totalIpu: bigint('total_ipu', { mode: 'number' }).default(0),
    totalCost: bigint('total_cost', { mode: 'number' }).default(0),
    advertiserId: varchar('advertiser_id', { length: 255 }).default('999999'),
    postBackTime: timestamp('post_back_time'),
    openGm: int('open_gm').default(0),
    openFreeAd: int('open_free_ad').default(0)
});
export const userDataInsertSchema = createInsertSchema(userData);
export const userDataSelectSchema = createSelectSchema(userData);

export const userSubscribe = mysqlTable('nqhy_subscribe', {
    id: varchar('id', { length: 255 }).primaryKey(),
    openId: varchar('open_id', { length: 255 }).notNull(),
    tplId: varchar('tpl_Id', { length: 255 }).notNull(),
    status: varchar('status', { length: 32 }).notNull(),
    platform: varchar('platform', { length: 255 }),
    createdAt: timestamp('created_at'),
    updatedAt: timestamp('updated_at'),
    count: int('count'),
    lastReceivedAt: timestamp('last_received_at') // 用户上次领取消息的时间
});

export const userSubscribeInsertSchema = createInsertSchema(userSubscribe);
export const userSubscribeSelectSchema = createSelectSchema(userSubscribe);

// 星通互娱登录事件表
export const xthyLoginData = mysqlTable('xthy_login_data', {
    nonId: bigint('non_id', { mode: 'number' }).primaryKey().autoincrement(), // 无意义编号
    appId: varchar('app_id', { length: 255 }), // appId
    openId: varchar('open_id', { length: 255 }).notNull(), // openId
    clickId: varchar('click_id', { length: 255 }), // clickId
    clickIdCallbackFlag: varchar('click_id_callback_flag', { length: 10 }).default('N'), // clickid是否已回传
    adId: varchar('ad_id', { length: 255 }), // ad_id
    clickTime: datetime('click_time'), // click事件时间
    createTime: datetime('create_time').default(new Date()), // 创建时间
    platform: varchar('platform', { length: 50 }), // 设备类型
    updateTime: datetime('update_time').default(new Date()), // 修改时间
    postBackTime: datetime('post_back_time'), // 回传时间
}, (table) => ({
    // 创建索引
    clickIdIdx: index('idx_click_id').on(table.clickId),
    openIdIdx: index('idx_open_id').on(table.openId),
}));

export const xthyLoginDataInsertSchema = createInsertSchema(xthyLoginData);
export const xthyLoginDataSelectSchema = createSelectSchema(xthyLoginData);

// 星通互娱用户信息表
export const xthyUserData = mysqlTable('xthy_user_data', {
    nonId: bigint('non_id', { mode: 'number' }).primaryKey().autoincrement(), // 无意义编号
    appId: varchar('app_id', { length: 255 }), // appid
    openId: varchar('open_id', { length: 64 }), // openid
    unionId: varchar('union_id', { length: 255 }), // union_id
    latestAdId: varchar('latest_ad_id', { length: 255 }), // 最新adId
    latestClickId: varchar('latest_click_id', { length: 300 }), // 最新clickid
    clickIdCallbackFlag: varchar('click_id_callback_flag', { length: 10 }).default('N'), // clickid是否已回传
    createTime: datetime('create_time').default(new Date()), // 创建时间
    platform: varchar('platform', { length: 50 }), // 设备类型
    updateTime: datetime('update_time').default(new Date()), // 修改时间
    totalIpu: bigint('total_ipu', { mode: 'number' }).default(0), // 总IPU
    totalCost: bigint('total_cost', { mode: 'number' }).default(0), // 总成本
    advertiserId: varchar('advertiser_id', { length: 30 }).default('999999'), // 广告户id
    postBackTime: datetime('post_back_time'), // 回传时间
    openGm: int('open_gm').default(0), // 1开启GM，0关闭GM
    openFreeAd: int('open_free_ad').default(0), // 1开启免广告，0关闭
}, (table) => ({
    // 创建索引
    openIdIdx: index('idx_open_id').on(table.openId),
    openIdClickFlagIdx: index('idx_openid_clickflag').on(table.openId, table.clickIdCallbackFlag),
}));

export const xthyUserDataInsertSchema = createInsertSchema(xthyUserData);
export const xthyUserDataSelectSchema = createSelectSchema(xthyUserData);

// 定时任务配置表 - 合并设计，消息数据存储为JSON数组
export const scheduledTasks = mysqlTable('nqhy_message_tasks', {
    id: bigint('id', { mode: 'number' }).primaryKey().autoincrement(),
    tplId: varchar('tpl_id', { length: 255 }).notNull(),
    platform: varchar('platform', { length: 10 }).notNull(),
    appId: varchar('app_id', { length: 255 }).notNull(),
    max: int('max').notNull().default(1),
    cronExpression: varchar('cron_expression', { length: 100 }).notNull(),
    messageData: json('message_data').notNull(), // 存储消息数组，如 [{"功能名称": "七日签到", "签到奖励": "盲盒x10"}, {...}]
    intervalHours: int('interval_hours').notNull().default(24), // 执行间隔时间（小时），默认24小时（每日一次）
    enabled: boolean('enabled').notNull().default(true),
    type: varchar('type', { length: 20 }).notNull().default('recurring'), // 任务类型：recurring(循环) 或 single(单次)
    taskType: varchar('task_type', { length: 20 }).notNull().default('message'), // 任务执行类型：message(消息) 或 card(卡片)
    meta: json('meta'), // 元数据，JSON格式，包含任务的额外配置信息
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
    lastExecutedAt: timestamp('last_executed_at'),
}, (table) => ({
    tplIdIndex: index('idx_tpl_id').on(table.tplId),
    enabledIndex: index('idx_enabled').on(table.enabled),
}));

// Schema导出
export const scheduledTasksInsertSchema = createInsertSchema(scheduledTasks);
export const scheduledTasksSelectSchema = createSelectSchema(scheduledTasks);

