import { mysqlTable, varchar, int, timestamp, bigint, index, boolean, text, json } from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { randomUUID } from 'crypto';

// 事件数据模型 (按日期分表)
export const eventData = mysqlTable('event_data', {
    id: bigint('id', { mode: 'number' }).primaryKey().autoincrement(),
    openId: varchar('open_id', { length: 255 }),
    eventCode: varchar('event_code', { length: 255 }),
    eventTime: timestamp('event_time'),
    uuid: varchar('uuid', { length: 255 }).unique(),
    appId: varchar('app_id', { length: 255 })
});
export const eventDataInsertSchema = createInsertSchema(eventData);
export const eventDataSelectSchema = createSelectSchema(eventData);

// 用户数据模型
export const userData = mysqlTable('xthy_user_data', {
    id: bigint('non_id', { mode: 'number' }).primaryKey().autoincrement(),
    appId: varchar('app_id', { length: 255 }),
    openId: varchar('open_id', { length: 255 }),
    unionId: varchar('union_id', { length: 255 }), 
    latestAdId: varchar('latest_ad_id', { length: 255 }),
    latestClickId: varchar('latest_click_id', { length: 255 }),
    clickIdCallbackFlag: varchar('click_id_callback_flag', { length: 1 }).default('N'),
    createTime: timestamp('create_time').defaultNow(),
    platform: varchar('platform', { length: 255 }),
    updateTime: timestamp('update_time').defaultNow(),
    totalIpu: bigint('total_ipu', { mode: 'number' }).default(0),
    totalCost: bigint('total_cost', { mode: 'number' }).default(0),
    advertiserId: varchar('advertiser_id', { length: 255 }).default('999999'),
    postBackTime: timestamp('post_back_time'),
    openGm: int('open_gm').default(0),
    openFreeAd: int('open_free_ad').default(0)
});
export const userDataInsertSchema = createInsertSchema(userData);
export const userDataSelectSchema = createSelectSchema(userData);

export const userSubscribe = mysqlTable('nqhy_subscribe', {
    id: varchar('id', { length: 255 }).primaryKey(),
    openId: varchar('open_id', { length: 255 }).notNull(),
    tplId: varchar('tpl_Id', { length: 255 }).notNull(),
    status: varchar('status', { length: 32 }).notNull(),
    platform: varchar('platform', { length: 255 }),
    createdAt: timestamp('created_at'),
    updatedAt: timestamp('updated_at'),
    count: int('count'),
    lastReceivedAt: timestamp('last_received_at') // 用户上次领取消息的时间
});
export const userSubscribeInsertSchema = createInsertSchema(userSubscribe);
export const userSubscribeSelectSchema = createSelectSchema(userSubscribe);

// 定时任务配置表 - 合并设计，消息数据存储为JSON数组
export const scheduledTasks = mysqlTable('nqhy_message_tasks', {
    id: bigint('id', { mode: 'number' }).primaryKey().autoincrement(),
    tplId: varchar('tpl_id', { length: 255 }).notNull(),
    platform: varchar('platform', { length: 10 }).notNull(),
    appId: varchar('app_id', { length: 255 }).notNull(),
    max: int('max').notNull().default(1),
    cronExpression: varchar('cron_expression', { length: 100 }).notNull(),
    messageData: json('message_data').notNull(), // 存储消息数组，如 [{"功能名称": "七日签到", "签到奖励": "盲盒x10"}, {...}]
    intervalHours: int('interval_hours').notNull().default(24), // 执行间隔时间（小时），默认24小时（每日一次）
    enabled: boolean('enabled').notNull().default(true),
    type: varchar('type', { length: 20 }).notNull().default('recurring'), // 任务类型：recurring(循环) 或 single(单次)
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
    lastExecutedAt: timestamp('last_executed_at'), // 上次执行时间，专门用于判断任务执行间隔
}, (table) => ({
    tplIdIndex: index('idx_tpl_id').on(table.tplId),
    enabledIndex: index('idx_enabled').on(table.enabled),
}));

// Schema导出
export const scheduledTasksInsertSchema = createInsertSchema(scheduledTasks);
export const scheduledTasksSelectSchema = createSelectSchema(scheduledTasks);

