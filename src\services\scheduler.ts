import { getMessageTasks, ScheduledMessageTask, } from '@/config'
import { sendBatchSubscribeMessages } from '../services/douyin-message'
import { shouldExecuteTask } from '@/common'
import { distributedLock } from './distributed-lock'
import { schedulerLogger } from '@/lib/logger'



/**
 * 执行单个消息发送任务
 */
async function executeMessageTask(task: ScheduledMessageTask) {
  if (!task.enabled) {
    schedulerLogger.info('任务已禁用，跳过执行', { tplId: task.tplId })
    return
  }

  try {
    schedulerLogger.info('开始执行消息发送任务', { tplId: task.tplId })

    const result = await sendBatchSubscribeMessages(
      task
    )

    schedulerLogger.info('任务执行完成', {
      tplId: task.tplId,
      totalUsers: result.totalUsers,
      successCount: result.results.filter(r => r.success).length,
      failCount: result.results.filter(r => !r.success).length
    })
  } catch (error) {
    schedulerLogger.error('任务执行失败', {
      tplId: task.tplId,
      error: error.message
    })
  }
}

/**
 * 检查并执行所有到期的定时任务
 */
export async function checkAndExecuteScheduledTasks() {
  schedulerLogger.debug('检查定时任务', { timestamp: new Date().toISOString() })

  // 从数据库加载最新的任务配置
  const messageTasks = await getMessageTasks()

  if (messageTasks.length === 0) {
    schedulerLogger.debug('没有找到启用的定时任务')
    return
  }

  for (const task of messageTasks) {
    if (shouldExecuteTask(task.cronExpression)) {
      // 为每个任务创建唯一的锁键
      const lockKey = `task_${task.tplId}_${task.cronExpression}`
      await executeMessageTask(task)
      // 使用分布式锁执行任务，防止多实例重复执行
      // await distributedLock.executeWithLock(
      //   lockKey,
      //   async () => {
      //     console.log(1111)

      //   },
      //   600 // 10分钟锁过期时间，给任务足够的执行时间
      // )
    }
  }
}

/**
 * 启动定时任务调度器
 * 每分钟检查一次是否有任务需要执行
 */
export function startScheduler() {
  // 检查是否启用定时任务
  if (process.env.SCHEDULER_ENABLED === 'false') {
    schedulerLogger.info('定时任务调度器已禁用')
    return
  }

  // 检查是否为主实例（可选配置）
  const isMainInstance = process.env.SCHEDULER_MAIN_INSTANCE !== 'false'
  if (!isMainInstance) {
    schedulerLogger.info('当前实例不是主实例，跳过启动定时任务调度器')
    return
  }

  schedulerLogger.info('启动定时任务调度器', {
    nodeEnv: process.env.NODE_ENV,
    instanceId: process.env.INSTANCE_ID || 'unknown'
  })

  // 立即执行一次检查
  checkAndExecuteScheduledTasks()

  // 每分钟检查一次
  const intervalId = setInterval(() => {
    checkAndExecuteScheduledTasks()
  }, 60 * 1000) // 60秒

  // 优雅关闭时清理定时器
  process.on('SIGTERM', () => {
    schedulerLogger.info('收到SIGTERM信号，停止定时任务调度器')
    clearInterval(intervalId)
  })

  process.on('SIGINT', () => {
    schedulerLogger.info('收到SIGINT信号，停止定时任务调度器')
    clearInterval(intervalId)
  })
}

/**
 * 手动触发特定模板的消息发送
 */
export async function triggerMessageTask(tplId: string, customData?: Record<string, string>[]) {
  // 从数据库加载最新的任务配置
  const messageTasks = await getMessageTasks()
  const task = messageTasks.find((t: ScheduledMessageTask) => t.tplId === tplId)
  if (!task) {
    throw new Error(`未找到模板ID为 ${tplId} 的任务配置`)
  }

  const taskToExecute: ScheduledMessageTask = {
    ...task,
    messageData: customData || task.messageData
  }

  // 手动触发的任务也使用分布式锁，防止与定时任务冲突
  const lockKey = `manual_task_${task.tplId}_${Date.now()}`

  await distributedLock.executeWithLock(
    lockKey,
    async () => {
      await executeMessageTask(taskToExecute)
    },
    60 // 1分钟锁过期时间
  )
}

// 导出获取任务配置的函数
export { getMessageTasks }