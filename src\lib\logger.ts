import pino from 'pino'
import pretty from 'pino-pretty'
import fs from 'fs'
import path from 'path'

// 日志级别类型
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal'

// 获取日志级别
function getLogLevel(): LogLevel {
  const level = process.env.LOG_LEVEL?.toLowerCase() as LogLevel
  if (['trace', 'debug', 'info', 'warn', 'error', 'fatal'].includes(level)) {
    return level
  }
  return process.env.NODE_ENV === 'production' ? 'info' : 'debug'
}

// 判断是否使用pretty格式
function shouldUsePretty(): boolean {
  return process.env.NODE_ENV !== 'production' && process.env.LOG_FORMAT !== 'json'
}

// 判断是否启用文件日志
function shouldLogToFile(): boolean {
  return process.env.LOG_TO_FILE === 'true' || process.env.NODE_ENV === 'production'
}

// 获取日志目录
function getLogDir(): string {
  return process.env.LOG_DIR || 'logs'
}

// 确保日志目录存在
function ensureLogDir(): string {
  const logDir = getLogDir()
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true })
  }
  return logDir
}

// 创建基础Pino配置
function createBaseConfig() {
  return {
    level: getLogLevel(),
    timestamp: () => `,"time":"${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}"`,
    formatters: {
      level: (label: string) => ({ level: label }),
      bindings: (bindings: any) => ({
        pid: bindings.pid,
        hostname: bindings.hostname,
        service: 'nqhy-backend',
      }),
    },
    serializers: {
      req: (req: any) => ({
        method: req.method,
        url: req.url,
        headers: {
          'user-agent': req.headers?.['user-agent'],
          'content-type': req.headers?.['content-type'],
          'authorization': req.headers?.authorization ? '[REDACTED]' : undefined,
        },
        remoteAddress: req.socket?.remoteAddress,
        remotePort: req.socket?.remotePort,
      }),
      res: (res: any) => ({
        statusCode: res.statusCode,
        headers: res.getHeaders?.(),
      }),
      err: pino.stdSerializers.err,
    },
  }
}

// 创建logger实例
function createLogger() {
  const baseConfig = createBaseConfig()

  if (shouldUsePretty()) {
    return pino(baseConfig, pretty({
      colorize: process.platform !== 'win32', // Windows下禁用颜色
      translateTime: 'yyyy-mm-dd HH:MM:ss.l',
      ignore: 'pid,hostname',
      messageFormat: '[{service}] {msg}',
      customPrettifiers: {
        level: (level: string) => `[${level.toUpperCase()}]`,
      },
    }))
  } else {
    return pino(baseConfig)
  }
}

// 导出全局logger实例
export const logger = createLogger()

// 导出便捷的日志方法
export const log = {
  trace: (msg: string, obj?: any) => logger.trace(obj, msg),
  debug: (msg: string, obj?: any) => logger.debug(obj, msg),
  info: (msg: string, obj?: any) => logger.info(obj, msg),
  warn: (msg: string, obj?: any) => logger.warn(obj, msg),
  error: (msg: string, obj?: any) => logger.error(obj, msg),
  fatal: (msg: string, obj?: any) => logger.fatal(obj, msg),
}

// 创建子logger
export function createChildLogger(bindings: Record<string, any>) {
  return logger.child(bindings)
}

// Redis相关的日志工具
export const redisLogger = createChildLogger({ component: 'redis' })

// 数据库相关的日志工具
export const dbLogger = createChildLogger({ component: 'database' })

// API相关的日志工具
export const apiLogger = createChildLogger({ component: 'api' })

// SSH隧道相关的日志工具
export const sshLogger = createChildLogger({ component: 'ssh-tunnel' })

// 定时任务相关的日志工具
export const schedulerLogger = createChildLogger({ component: 'scheduler' })

// 抖音服务相关的日志工具
export const douyinLogger = createChildLogger({ component: 'douyin' })

export default logger
